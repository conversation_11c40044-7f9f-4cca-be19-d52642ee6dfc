# 最大并发线程数
max_workers: 3
# 请求超时时间（秒）
request_timeout: 15
# 最大重试次数
max_retries: 3
# 最大冷却时间, 单位ms
max_wait_time: 1200
# 最小冷却时间, 单位ms
min_wait_time: 1000
# 最小连接超时时间
min_connect_timeout: 3.05
# 强制退出等待时间
force_exit_timeout: 5
# 是否启用优雅退出
graceful_exit: true
# 保存小说格式, 可选: [txt, epub]
novel_format: txt
# 是否自动清理缓存文件
auto_clear_dump: true
# 下载状态文件名
status_filename: chapter_status.json
# Cookie存储文件名
cookie_filename: session_cookie.json
# 保存路径
save_path: D:/app/useful_novel_tool/download
# 七猫小说保存路径
qimao_save_dir: D:/app/useful_novel_tool/download
# 使用官方API
use_official_api: true
# 自动生成
iid: '3924570615610586'
# API列表
api_endpoints: []
# 是否启用激活检查
activation_check: true
# 激活码
activation_token: ''
# 机器码
machine_code: ''
# 是否自动检查更新
auto_check_update: true
# 更新检查间隔（小时）
update_check_interval: 24
# 上次检查更新时间戳
last_update_check: 1755695622
# 客户端版本号
client_version: 2.0.0
# 任务查询间隔（秒）
task_check_interval: 5
# 最大任务查询次数
task_max_check_times: 60